// Simple JWT structure test
const token = "eyJhbGciOiJFUzI1NiIsImtpZCI6IjUxNTk5Yjg3LTYwMzQtNGEzNi1iYTBkLWNmMjQ4NjZmOGY3OSIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

const parts = token.split('.');
console.log(`Token parts: ${parts.length}`);
console.log(`Header: ${parts[0]}`);
console.log(`Payload: ${parts[1]}`);
console.log(`Signature: ${parts[2] || 'MISSING'}`);

if (parts.length === 3) {
    try {
        const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        
        console.log('\nHeader:', JSON.stringify(header, null, 2));
        console.log('\nPayload:', JSON.stringify(payload, null, 2));
        console.log('\nToken is valid JWT structure');
    } catch (e) {
        console.log('Error parsing JWT:', e.message);
    }
} else {
    console.log('Invalid JWT structure - should have 3 parts separated by dots');
}
