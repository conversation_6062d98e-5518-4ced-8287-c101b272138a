using AuthService.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using DotNetEnv;

// Create a logger for startup configuration
using var loggerFactory = LoggerFactory.Create(builder =>
{
    builder.AddConsole();
    builder.SetMinimumLevel(LogLevel.Debug);
});
var logger = loggerFactory.CreateLogger<Program>();

// Load environment variables from .env file if it exists
if (File.Exists(Path.Combine(Directory.GetCurrentDirectory(), ".env")))
{
    Env.Load();
    logger.LogDebug("Loaded environment variables from .env file");
}
else
{
    logger.LogDebug("No .env file found, using system environment variables");
}

var builder = WebApplication.CreateBuilder(args);

// ASP.NET Core automatically loads environment variables - no additional setup needed
logger.LogDebug("Using ASP.NET Core native configuration (environment variables + appsettings.json)");

// Load Supabase URL and Project ID from config
var supabaseUrl = builder.Configuration["SUPABASE_URL"];
var supabaseProjectId = builder.Configuration["SUPABASE_PROJECT_ID"];  

// Only require Supabase configuration if not in testing environment
if (!builder.Environment.IsEnvironment("Testing"))
{
    if (string.IsNullOrEmpty(supabaseUrl))
    {
        throw new InvalidOperationException("SUPABASE_URL must be configured");
    }

    if (string.IsNullOrEmpty(supabaseProjectId))
    {
        throw new InvalidOperationException("SUPABASE_PROJECT_ID must be configured");
    }
}

// Configure Authentication only if not in testing environment
if (!builder.Environment.IsEnvironment("Testing") && !string.IsNullOrEmpty(supabaseUrl) && !string.IsNullOrEmpty(supabaseProjectId))
{
        builder.Services
        .AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddJwtBearer(options =>
        {
            // Configure for Supabase JWT validation
            options.Authority = $"{supabaseUrl}/auth/v1";
            options.RequireHttpsMetadata = !builder.Environment.IsDevelopment();

            // Basic token validation settings for Supabase tokens
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer           = true,
                ValidIssuer              = $"{supabaseUrl}/auth/v1",
                ValidateAudience         = true,
                ValidAudiences           = ["authenticated", supabaseProjectId], // Support both audience types
                ValidateLifetime         = true,
                ClockSkew                = TimeSpan.FromSeconds(30),
                ValidateIssuerSigningKey = true,
                RequireSignedTokens      = true,
                RequireExpirationTime    = true
            };

            // Note: IssuerSigningKeyResolver will be configured later using options pattern
    
            // Enhanced logging and debugging for token validation
            options.Events = new JwtBearerEvents
            {
                OnAuthenticationFailed = context =>
                {
                    var logger = context.HttpContext.RequestServices
                        .GetRequiredService<ILogger<Program>>();
                    
                    if (context.Exception is SecurityTokenInvalidSignatureException)
                    {
                        logger.LogError("JWT signature validation failed: {Error}", context.Exception.Message);
                        
                        // Try to extract the kid from the token for debugging
                        try
                        {
                            var token = context.Request.Headers.Authorization.ToString().Replace("Bearer ", "");
                            var parts = token.Split('.');
                            if (parts.Length >= 2)
                            {
                                var base64Url = parts[0];
                                var base64 = base64Url.PadRight(base64Url.Length + (4 - base64Url.Length % 4) % 4, '=')
                                    .Replace('-', '+')
                                    .Replace('_', '/');
                                    
                                var headerJson = Encoding.UTF8.GetString(Convert.FromBase64String(base64));
                                var headerData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(headerJson);
                                
                                if (headerData != null && headerData.TryGetValue("kid", out var kidValue))
                                {
                                    logger.LogError("Token has kid: {KeyId}, but signature validation failed", kidValue);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogWarning(ex, "Error extracting kid from token for debugging");
                        }
                    }
                    else if (context.Exception is SecurityTokenSignatureKeyNotFoundException)
                    {
                        logger.LogError("JWT signing key not found: {Error}", context.Exception.Message);
                        logger.LogInformation("Make sure the JWKS endpoint is accessible and contains the required key");
                    }
                    else if (context.Exception is SecurityTokenInvalidAudienceException)
                    {
                        logger.LogError("JWT audience validation failed: {Error}", context.Exception.Message);
                        logger.LogInformation("Expected audiences: authenticated, {ProjectId}. Check Flutter client token audience claim.", supabaseProjectId);
                    }
                    else if (context.Exception is SecurityTokenInvalidIssuerException)
                    {
                        logger.LogError("JWT issuer validation failed: {Error}", context.Exception.Message);
                        logger.LogInformation("Expected issuer: {ExpectedIssuer}. Check Supabase URL configuration.", $"{supabaseUrl}/auth/v1");
                    }
                    else
                    {
                        logger.LogWarning("JWT Authentication failed: {Error}, {ExceptionType}",
                            context.Exception.Message, context.Exception.GetType().Name);
                    }
                    
                    return Task.CompletedTask;
                },
                OnMessageReceived = context =>
                {
                    var logger = context.HttpContext.RequestServices
                        .GetRequiredService<ILogger<Program>>();
                    
                    var token = context.Token;
                    if (!string.IsNullOrEmpty(token))
                    {
                        // Log token header for debugging (without exposing the full token)
                        var parts = token.Split('.');
                        if (parts.Length >= 2)
                        {
                            try
                            {
                                var base64Url = parts[0];
                                var base64 = base64Url.PadRight(base64Url.Length + (4 - base64Url.Length % 4) % 4, '=')
                                    .Replace('-', '+')
                                    .Replace('_', '/');
                                    
                                var headerJson = Encoding.UTF8.GetString(Convert.FromBase64String(base64));
                                logger.LogDebug("JWT Header: {Header}", headerJson);
                                
                                // Parse the header to extract algorithm info
                                var headerData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(headerJson);
                                if (headerData != null && headerData.TryGetValue("alg", out var alg))
                                {
                                    // Store algorithm in HttpContext.Items for later access
                                    context.HttpContext.Items["jwt_algorithm"] = alg.ToString();
                                    logger.LogInformation("JWT uses algorithm: {Algorithm}", alg);
                                    
                                    if (headerData.TryGetValue("kid", out var kid))
                                    {
                                        context.HttpContext.Items["jwt_key_id"] = kid.ToString();
                                        logger.LogInformation("JWT uses key ID: {KeyId}", kid);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                logger.LogWarning(ex, "Error parsing JWT header");
                            }
                        }
                    }
                    
                    return Task.CompletedTask;
                },
                OnTokenValidated = context =>
                {
                    var logger = context.HttpContext.RequestServices
                        .GetRequiredService<ILogger<Program>>();

                    var userId = context.Principal?.FindFirst("sub")?.Value;
                    var audience = context.Principal?.FindFirst("aud")?.Value;
                    var issuer = context.Principal?.FindFirst("iss")?.Value;
                    var role = context.Principal?.FindFirst("role")?.Value;

                    logger.LogInformation("JWT validated successfully - User: {UserId}, Audience: {Audience}, Issuer: {Issuer}, Role: {Role}",
                        userId, audience, issuer, role);

                    return Task.CompletedTask;
                }
            };
        });
}

// Configure CORS for frontend access
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

builder.Services.AddControllers();
builder.Services.AddAuthorization();

// Configure data protection to suppress warnings in containerized environments
builder.Services.AddDataProtection()
    .SetApplicationName("auth-service");

builder.Services.AddSwaggerGen();

// Register SupabaseClient and dependencies for ISupabaseClient
// Register HttpClient and MemoryCache
builder.Services.AddHttpClient();
builder.Services.AddMemoryCache();

// Register services
builder.Services.AddScoped<ICacheService, MemoryCacheService>();
builder.Services.AddScoped<ISupabaseClient, SupabaseClient>();

// Program.cs, before builder.Build()

// Needed to grab services in IssuerSigningKeyResolver
builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

// Memory cache is already registered by AddMemoryCache()

// Register JwksService and related services
var jwksUri = $"{supabaseUrl}/auth/v1/jwks";

// Log the JWKS URI for debugging
logger.LogInformation("Configuring JWKS service with URI: {JwksUri}", jwksUri);

// Register JwksCache as a singleton
builder.Services.AddSingleton<JwksCache>(_ =>
{
    var logger = _.GetRequiredService<ILogger<JwksCache>>();
    return new JwksCache(logger);
});

// Register JwksService with the cache
builder.Services.AddSingleton<JwksService>(_ =>
{
    var httpClient = _.GetRequiredService<IHttpClientFactory>().CreateClient();

    // Configure the HttpClient with a timeout
    httpClient.Timeout = TimeSpan.FromSeconds(10);

    var memoryCache = _.GetRequiredService<IMemoryCache>();
    var logger = _.GetRequiredService<ILogger<JwksService>>();
    var jwksCache = _.GetRequiredService<JwksCache>();

    // Log the JWKS URI again at service creation time
    logger.LogInformation("Creating JwksService with URI: {JwksUri}", jwksUri);

    return new JwksService(
        httpClient,
        memoryCache,
        logger,
        jwksUri,
        TimeSpan.FromHours(24), // Cache keys for 24 hours
        jwksCache
    );
});

// Register JwksBackgroundService as a hosted service
builder.Services.AddHostedService<JwksBackgroundService>(_ =>
{
    var jwksService = _.GetRequiredService<JwksService>();
    var jwksCache = _.GetRequiredService<JwksCache>();
    var logger = _.GetRequiredService<ILogger<JwksBackgroundService>>();

    // Refresh keys every hour
    return new JwksBackgroundService(
        jwksService,
        jwksCache,
        logger,
        TimeSpan.FromHours(1)
    );
});

// Configure JWT Bearer to use JwksService in a way that avoids BuildServiceProvider
builder.Services.AddOptions<JwtBearerOptions>(JwtBearerDefaults.AuthenticationScheme)
    .Configure<IServiceProvider>((options, serviceProvider) =>
    {
        // Get JwksService from DI container when options are being configured
        var jwksService = serviceProvider.GetRequiredService<JwksService>();
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        
        // Set up the key resolver using the synchronous method from JwksService
        options.TokenValidationParameters.IssuerSigningKeyResolver = (token, securityToken, kid, validationParameters) =>
        {
            logger.LogDebug("Resolving signing key for kid: {KeyId}", kid);
            
            if (string.IsNullOrEmpty(kid))
            {
                logger.LogWarning("Token has no key ID (kid), cannot resolve signing key");
                return [];
            }

            var key = jwksService.GetSigningKey(kid);

            if (key != null)
            {
                logger.LogDebug("Found signing key for kid: {KeyId}, type: {KeyType}", kid, key.GetType().Name);
                return [key];
            }
            else
            {
                logger.LogWarning("No signing key found for kid: {KeyId}", kid);
                return [];
            }
        };
    });

builder.Services.AddHealthChecks();


var app = builder.Build();

// Test comment for deployment detection

// Configure middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Auth Service API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
}

// Only use HTTPS redirection in development or when HTTPS is properly configured
if (app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

// Add error handling endpoint
app.Map("/error", (ILogger<Program> logger) =>
{
    logger.LogError("An unhandled exception occurred in Auth service");
    return Results.Problem("An error occurred while processing your request.");
});

// Add a root endpoint for API information
app.MapGet("/", () => new
{
    service = "Auth Service",
    version = "1.0.0",
    endpoints = new
    {
        authentication = AuthenticationEndpoints,
        user_management = UserManagementEndpoints,
        user_info = UserInfoEndpoints,
        oauth = OAuthEndpoints,
        settings = SettingsEndpoints,
        health = HealthEndpoints
    }
});

// Add test endpoints to check JWKS cache status
app.MapGet("/jwks-cache-status", (JwksCache jwksCache, JwksService jwksService) =>
{
    return new
    {
        CacheLastUpdated = jwksCache.LastUpdated.ToString("o"),
        KeyCount = jwksCache.Count,
        jwksCache.IsEmpty,
        Keys = jwksCache.GetAllKeys().Select(k => new
        {
            k.Key,
            KeyType = k.Value.GetType().Name
        }).ToList()
    };
}).RequireAuthorization();

// Add a non-authenticated endpoint for monitoring
app.MapGet("/health/jwks-cache", (JwksCache jwksCache) =>
{
    var isHealthy = !jwksCache.IsEmpty && jwksCache.Count > 0;

    return Results.Ok(new
    {
        Status = isHealthy ? "Healthy" : "Unhealthy",
        CacheLastUpdated = jwksCache.LastUpdated.ToString("o"),
        KeyCount = jwksCache.Count
    });
});

// Add a test endpoint to validate Flutter client tokens
app.MapGet("/auth/test-token", (HttpContext context) =>
{
    var user = context.User;

    if (!user.Identity?.IsAuthenticated == true)
    {
        return Results.Unauthorized();
    }

    var claims = user.Claims.ToDictionary(c => c.Type, c => c.Value);

    return Results.Ok(new
    {
        Message = "Token validation successful!",
        UserId = user.FindFirst("sub")?.Value,
        Email = user.FindFirst("email")?.Value,
        Role = user.FindFirst("role")?.Value,
        Audience = user.FindFirst("aud")?.Value,
        Issuer = user.FindFirst("iss")?.Value,
        ExpiresAt = user.FindFirst("exp")?.Value,
        IssuedAt = user.FindFirst("iat")?.Value,
        AllClaims = claims
    });
}).RequireAuthorization();

app.Run();

// Make the implicit Program class public for testing
public partial class Program
{
    private static readonly string[] AuthenticationEndpoints =
    [
        "POST /auth/signup - User signup",
        "POST /auth/login - User login",
        "POST /auth/token/refresh - Refresh access token",
        "POST /auth/recover - Password recovery",
        "POST /auth/otp - Send OTP",
        "POST /auth/verify - Verify OTP",
        "POST /auth/logout - User logout"
    ];

    private static readonly string[] UserManagementEndpoints =
    [
        "GET /auth/user - Get current user",
        "PUT /auth/user - Update user",
        "GET /auth/profile - Get user profile",
        "POST /auth/profile/link-broker - Link broker to profile"
    ];

    private static readonly string[] UserInfoEndpoints =
    [
        "GET /auth/user/me - Get current user from JWT claims",
        "GET /auth/user/profile - Get user profile",
        "GET /auth/user/role/{roleName} - Check if user has specific role",
        "GET /auth/user/trader-only - Trader-only endpoint (requires IsTrader policy)",
        "GET /auth/user/admin-only - Admin-only endpoint (requires IsAdmin policy)",
        "GET /auth/user/broker-required - Requires linked broker (HasBroker policy)"
    ];

    private static readonly string[] OAuthEndpoints =
    [
        "POST /auth/oauth - Get OAuth URL",
        "POST /auth/oauth/callback - OAuth callback"
    ];

    private static readonly string[] SettingsEndpoints =
    [
        "GET /auth/settings - Get auth settings"
    ];

    private static readonly string[] HealthEndpoints =
    [
        "GET /health - Health check",
        "GET /jwks-cache-status - JWKS cache status",
        "GET /auth/test-token - Test token validation (requires auth)"
    ];
}

