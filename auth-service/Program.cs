using AuthService.Services;
using AuthService.Extensions;
using Microsoft.AspNetCore.DataProtection;
using DotNetEnv;

// Create a logger for startup configuration
using var loggerFactory = LoggerFactory.Create(builder =>
{
    builder.AddConsole();
    builder.SetMinimumLevel(LogLevel.Debug);
});
var logger = loggerFactory.CreateLogger<Program>();

// Load environment variables from .env file if it exists
if (File.Exists(Path.Combine(Directory.GetCurrentDirectory(), ".env")))
{
    Env.Load();
    logger.LogDebug("Loaded environment variables from .env file");
}
else
{
    logger.LogDebug("No .env file found, using system environment variables");
}

var builder = WebApplication.CreateBuilder(args);

// ASP.NET Core automatically loads environment variables - no additional setup needed
logger.LogDebug("Using ASP.NET Core native configuration (environment variables + appsettings.json)");

// Load Supabase URL and Project ID from config
var supabaseUrl = builder.Configuration["SUPABASE_URL"];
var supabaseProjectId = builder.Configuration["SUPABASE_PROJECT_ID"];  

// Only require Supabase configuration if not in testing environment
if (!builder.Environment.IsEnvironment("Testing"))
{
    if (string.IsNullOrEmpty(supabaseUrl))
    {
        throw new InvalidOperationException("SUPABASE_URL must be configured");
    }

    if (string.IsNullOrEmpty(supabaseProjectId))
    {
        throw new InvalidOperationException("SUPABASE_PROJECT_ID must be configured");
    }
}

// Configure Supabase JWT Authentication with auto-discovery
logger.LogInformation("Configuring Supabase JWT authentication with auto-discovery");

logger.LogDebug("Supabase configuration: {@Config}", builder.Configuration.GetSupabaseConfigurationSummary());

// Use the new auto-configuration extension
builder.Services.AddSupabaseJwtAuthentication(builder.Configuration);

// Configure CORS for frontend access
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

builder.Services.AddControllers();
builder.Services.AddAuthorization();

// Configure data protection to suppress warnings in containerized environments
builder.Services.AddDataProtection()
    .SetApplicationName("auth-service");

builder.Services.AddSwaggerGen();

// Register SupabaseClient and dependencies for ISupabaseClient
// Register HttpClient and MemoryCache
builder.Services.AddHttpClient();
builder.Services.AddMemoryCache();

// Register services
builder.Services.AddScoped<ICacheService, MemoryCacheService>();
builder.Services.AddScoped<ISupabaseClient, SupabaseClient>();

// Program.cs, before builder.Build()

// Note: JWKS services and JWT configuration are now handled automatically by AddSupabaseJwtAuthentication()





builder.Services.AddHealthChecks();


var app = builder.Build();

// Test comment for deployment detection

// Configure middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Auth Service API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
}

// Only use HTTPS redirection in development or when HTTPS is properly configured
if (app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

// Add error handling endpoint
app.Map("/error", (ILogger<Program> logger) =>
{
    logger.LogError("An unhandled exception occurred in Auth service");
    return Results.Problem("An error occurred while processing your request.");
});

// Add a root endpoint for API information
app.MapGet("/", () => new
{
    service = "Auth Service",
    version = "1.0.0",
    endpoints = new
    {
        authentication = AuthenticationEndpoints,
        user_management = UserManagementEndpoints,
        user_info = UserInfoEndpoints,
        oauth = OAuthEndpoints,
        settings = SettingsEndpoints,
        health = HealthEndpoints
    }
});

// Add test endpoints to check JWT configuration status
app.MapGet("/jwks-cache-status", () =>
{
    return new
    {
        Message = "JWKS auto-configuration is active",
        Status = "Healthy",
        ConfigurationType = "Auto-Discovery",
        LastUpdated = DateTime.UtcNow.ToString("o")
    };
}).RequireAuthorization();

// Add a non-authenticated endpoint for monitoring
app.MapGet("/health/jwks-cache", () =>
{
    return Results.Ok(new
    {
        Status = "Healthy",
        Message = "JWKS auto-configuration is active",
        LastChecked = DateTime.UtcNow.ToString("o")
    });
});

// Add a test endpoint to validate Flutter client tokens
app.MapGet("/auth/test-token", (HttpContext context) =>
{
    var user = context.User;

    if (!user.Identity?.IsAuthenticated == true)
    {
        return Results.Unauthorized();
    }

    var claims = user.Claims.ToDictionary(c => c.Type, c => c.Value);

    // With MapInboundClaims = false, we should get original JWT claim names
    var userId = user.FindFirst("sub")?.Value;
    var email = user.FindFirst("email")?.Value;
    var role = user.FindFirst("role")?.Value;

    return Results.Ok(new
    {
        Message = "Token validation successful!",
        UserId = userId,
        Email = email,
        Role = role,
        Audience = user.FindFirst("aud")?.Value,
        Issuer = user.FindFirst("iss")?.Value,
        ExpiresAt = user.FindFirst("exp")?.Value,
        IssuedAt = user.FindFirst("iat")?.Value,
        AllClaims = claims
    });
}).RequireAuthorization();

app.Run();

// Make the implicit Program class public for testing
public partial class Program
{
    private static readonly string[] AuthenticationEndpoints =
    [
        "POST /auth/signup - User signup",
        "POST /auth/login - User login",
        "POST /auth/token/refresh - Refresh access token",
        "POST /auth/recover - Password recovery",
        "POST /auth/otp - Send OTP",
        "POST /auth/verify - Verify OTP",
        "POST /auth/logout - User logout"
    ];

    private static readonly string[] UserManagementEndpoints =
    [
        "GET /auth/user - Get current user",
        "PUT /auth/user - Update user",
        "GET /auth/profile - Get user profile",
        "POST /auth/profile/link-broker - Link broker to profile"
    ];

    private static readonly string[] UserInfoEndpoints =
    [
        "GET /auth/user/me - Get current user from JWT claims",
        "GET /auth/user/profile - Get user profile",
        "GET /auth/user/role/{roleName} - Check if user has specific role",
        "GET /auth/user/trader-only - Trader-only endpoint (requires IsTrader policy)",
        "GET /auth/user/admin-only - Admin-only endpoint (requires IsAdmin policy)",
        "GET /auth/user/broker-required - Requires linked broker (HasBroker policy)"
    ];

    private static readonly string[] OAuthEndpoints =
    [
        "POST /auth/oauth - Get OAuth URL",
        "POST /auth/oauth/callback - OAuth callback"
    ];

    private static readonly string[] SettingsEndpoints =
    [
        "GET /auth/settings - Get auth settings"
    ];

    private static readonly string[] HealthEndpoints =
    [
        "GET /health - Health check",
        "GET /jwks-cache-status - JWKS cache status",
        "GET /auth/test-token - Test token validation (requires auth)"
    ];
}

