using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using System.Collections.Concurrent;
using System.Text.Json;

namespace AuthService.Services;

/// <summary>
/// Custom JWT configuration for Supabase that handles JWKS discovery with API key authentication
/// </summary>
public class SupabaseJwtConfiguration : IConfigureNamedOptions<JwtBearerOptions>
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<SupabaseJwtConfiguration> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ConcurrentDictionary<string, SecurityKey> _keyCache = new();
    private DateTime _lastKeyRefresh = DateTime.MinValue;
    private readonly TimeSpan _keyRefreshInterval = TimeSpan.FromHours(1);

    public SupabaseJwtConfiguration(
        IConfiguration configuration,
        ILogger<SupabaseJwtConfiguration> logger,
        IHttpClientFactory httpClientFactory)
    {
        _configuration = configuration;
        _logger = logger;
        _httpClientFactory = httpClientFactory;
    }

    public void Configure(string? name, JwtBearerOptions options)
    {
        if (name != JwtBearerDefaults.AuthenticationScheme)
            return;

        Configure(options);
    }

    public void Configure(JwtBearerOptions options)
    {
        var supabaseUrl = _configuration["SUPABASE_URL"];
        var supabaseProjectId = _configuration["SUPABASE_PROJECT_ID"];
        var supabaseApiKey = _configuration["SUPABASE_ANON_KEY"] ?? _configuration["SUPABASE_SERVICE_ROLE_KEY"];

        if (string.IsNullOrEmpty(supabaseUrl) || string.IsNullOrEmpty(supabaseProjectId))
        {
            _logger.LogWarning("Supabase configuration missing. JWT validation may not work properly.");
            return;
        }

        _logger.LogInformation("Configuring Supabase JWT validation for URL: {SupabaseUrl}", supabaseUrl);

        // Configure basic JWT options
        options.Authority = $"{supabaseUrl}/auth/v1";
        options.RequireHttpsMetadata = !IsTestEnvironment();
        options.SaveToken = true;

        // Configure token validation parameters
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = $"{supabaseUrl}/auth/v1",
            ValidateAudience = true,
            ValidAudiences = new[] { "authenticated", supabaseProjectId },
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            RequireSignedTokens = true,
            RequireExpirationTime = true,
            ClockSkew = TimeSpan.FromSeconds(30),

            // Disable automatic claim name mapping to preserve original JWT claim names
            NameClaimType = "sub",
            RoleClaimType = "role",

            // Custom key resolver that handles Supabase JWKS with API key
            IssuerSigningKeyResolver = (token, securityToken, kid, validationParameters) =>
            {
                return ResolveSigningKey(kid, supabaseUrl, supabaseApiKey);
            }
        };

        // Disable default claim name mapping to preserve original JWT claims
        options.MapInboundClaims = false;

        // Enhanced event handlers for debugging
        options.Events = new JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                LogAuthenticationFailure(context);
                return Task.CompletedTask;
            },
            OnTokenValidated = context =>
            {
                LogTokenValidationSuccess(context);
                return Task.CompletedTask;
            },
            OnMessageReceived = context =>
            {
                LogTokenReceived(context);
                return Task.CompletedTask;
            }
        };
    }

    private IEnumerable<SecurityKey> ResolveSigningKey(string? kid, string supabaseUrl, string? apiKey)
    {
        if (string.IsNullOrEmpty(kid))
        {
            _logger.LogWarning("Token has no key ID (kid), cannot resolve signing key");
            return Enumerable.Empty<SecurityKey>();
        }

        // Check cache first
        if (_keyCache.TryGetValue(kid, out var cachedKey) && 
            DateTime.UtcNow - _lastKeyRefresh < _keyRefreshInterval)
        {
            _logger.LogDebug("Using cached signing key for kid: {KeyId}", kid);
            return new[] { cachedKey };
        }

        // Fetch keys from JWKS endpoint
        try
        {
            var keys = FetchJwksKeys(supabaseUrl, apiKey).GetAwaiter().GetResult();
            
            if (keys.TryGetValue(kid, out var key))
            {
                _logger.LogDebug("Found signing key for kid: {KeyId}", kid);
                return new[] { key };
            }
            
            _logger.LogWarning("Signing key with kid {KeyId} not found in JWKS", kid);
            return Enumerable.Empty<SecurityKey>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving signing key for kid: {KeyId}", kid);
            return Enumerable.Empty<SecurityKey>();
        }
    }

    private async Task<ConcurrentDictionary<string, SecurityKey>> FetchJwksKeys(string supabaseUrl, string? apiKey)
    {
        var jwksUri = $"{supabaseUrl}/auth/v1/.well-known/jwks.json";
        
        using var httpClient = _httpClientFactory.CreateClient();
        httpClient.Timeout = TimeSpan.FromSeconds(10);
        
        // Add API key if available
        if (!string.IsNullOrEmpty(apiKey))
        {
            httpClient.DefaultRequestHeaders.Add("apikey", apiKey);
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
        }

        _logger.LogDebug("Fetching JWKS from: {JwksUri}", jwksUri);
        
        var response = await httpClient.GetAsync(jwksUri);
        response.EnsureSuccessStatusCode();
        
        var jwksJson = await response.Content.ReadAsStringAsync();

        var options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };

        var jwks = JsonSerializer.Deserialize<JsonWebKeySet>(jwksJson, options);

        var keys = new ConcurrentDictionary<string, SecurityKey>();

        if (jwks?.Keys != null)
        {
            foreach (var jwk in jwks.Keys)
            {
                if (string.IsNullOrEmpty(jwk.Kid))
                {
                    _logger.LogWarning("Skipping JWK with no Key ID");
                    continue;
                }

                try
                {
                    SecurityKey securityKey;

                    // Handle different key types like the existing JwksService
                    if (jwk.Kty == "EC" && jwk.Crv == "P-256")
                    {
                        // ES256 keys - use Microsoft.IdentityModel.Tokens.JsonWebKey
                        var jsonWebKey = new Microsoft.IdentityModel.Tokens.JsonWebKey(JsonSerializer.Serialize(jwk, options));
                        securityKey = jsonWebKey;
                    }
                    else if (jwk.Kty == "RSA")
                    {
                        // RSA keys - use Microsoft.IdentityModel.Tokens.JsonWebKey
                        var jsonWebKey = new Microsoft.IdentityModel.Tokens.JsonWebKey(JsonSerializer.Serialize(jwk, options));
                        securityKey = jsonWebKey;
                    }
                    else
                    {
                        _logger.LogWarning("Unsupported key type: {KeyType}, curve: {Curve}", jwk.Kty, jwk.Crv);
                        continue;
                    }

                    keys[jwk.Kid] = securityKey;
                    _logger.LogDebug("Added JWKS key: {KeyId}", jwk.Kid);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process JWKS key: {KeyId}", jwk.Kid);
                }
            }
        }

        // Update cache
        foreach (var (kid, key) in keys)
        {
            _keyCache[kid] = key;
        }
        _lastKeyRefresh = DateTime.UtcNow;
        
        _logger.LogInformation("Refreshed JWKS cache with {Count} keys", keys.Count);
        return keys;
    }

    private void LogAuthenticationFailure(AuthenticationFailedContext context)
    {
        var exceptionType = context.Exception.GetType().Name;
        _logger.LogError("JWT Authentication failed: {Error} ({ExceptionType})", 
            context.Exception.Message, exceptionType);

        // Specific error handling
        switch (context.Exception)
        {
            case SecurityTokenInvalidAudienceException:
                _logger.LogError("Invalid audience. Expected: authenticated or project ID");
                break;
            case SecurityTokenInvalidIssuerException:
                _logger.LogError("Invalid issuer. Expected: {ExpectedIssuer}", 
                    $"{_configuration["SUPABASE_URL"]}/auth/v1");
                break;
            case SecurityTokenSignatureKeyNotFoundException:
                _logger.LogError("Signing key not found. Check JWKS endpoint accessibility");
                break;
            case SecurityTokenInvalidSignatureException:
                _logger.LogError("Invalid token signature. Check token integrity");
                break;
        }
    }

    private void LogTokenValidationSuccess(TokenValidatedContext context)
    {
        var userId = context.Principal?.FindFirst("sub")?.Value;
        var email = context.Principal?.FindFirst("email")?.Value;
        var role = context.Principal?.FindFirst("role")?.Value;
        var audience = context.Principal?.FindFirst("aud")?.Value;

        _logger.LogInformation("JWT validation successful - User: {UserId}, Email: {Email}, Role: {Role}, Audience: {Audience}",
            userId, email, role, audience);
    }

    private void LogTokenReceived(MessageReceivedContext context)
    {
        if (!string.IsNullOrEmpty(context.Token))
        {
            _logger.LogDebug("JWT token received and being processed");
        }
    }

    private bool IsTestEnvironment()
    {
        var environment = _configuration["ASPNETCORE_ENVIRONMENT"];
        return string.Equals(environment, "Testing", StringComparison.OrdinalIgnoreCase) ||
               string.Equals(environment, "Test", StringComparison.OrdinalIgnoreCase);
    }
}

// Note: JsonWebKeySet and JsonWebKey classes are already defined in JwksService.cs
