using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using AuthService.Models;
using AuthService.Services;

namespace AuthService.Services;

    public class SupabaseClient : ISupabaseClient
    {
        private readonly HttpClient _httpClient;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ICacheService _cacheService;
        private readonly string _supabaseUrl;
        private readonly string _serviceRoleKey;
        private readonly string _anonKey;
        private readonly JsonSerializerOptions _jsonOptions;

        public SupabaseClient(HttpClient httpClient, IHttpClientFactory httpClientFactory, ICacheService cacheService, IConfiguration config)
        {
            _httpClient = httpClient;
            _httpClientFactory = httpClientFactory;
            _cacheService = cacheService;
        // Use ASP.NET Core configuration which automatically loads from environment variables
        _supabaseUrl = config["SUPABASE_URL"] 
            ?? throw new InvalidOperationException("SUPABASE_URL configuration is required");
        _serviceRoleKey = config["SUPABASE_SERVICE_ROLE_KEY"]
            ?? throw new InvalidOperationException("SUPABASE_SERVICE_ROLE_KEY configuration is required");
        _anonKey = config["SUPABASE_ANON_KEY"]
            ?? throw new InvalidOperationException("SUPABASE_ANON_KEY configuration is required");

            // Configure JSON options for System.Text.Json
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
                WriteIndented = false
            };

            // Set default headers for REST API calls
            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Bearer", _serviceRoleKey);
            _httpClient.DefaultRequestHeaders.Add("apikey", _serviceRoleKey);
        }

        public async Task<UserProfile?> GetUserProfileAsync(string userId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userId))
                    throw new ArgumentException("User ID cannot be null or empty", nameof(userId));

                // Create a client using the shared method
                using var client = CreateAuthHttpClient();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var url = $"/rest/v1/profiles?user_id=eq.{userId}&select=*";

                var response = await client.GetAsync(url);
                if (!response.IsSuccessStatusCode)
                {
                    if (response.StatusCode == HttpStatusCode.NotFound)
                    {
                        return null; // Profile not found
                    }
                    response.EnsureSuccessStatusCode();
                }

                var body = await response.Content.ReadAsStringAsync();
                var profiles = JsonSerializer.Deserialize<List<UserProfile>>(body, _jsonOptions);
                return profiles?.FirstOrDefault();
            }
            catch (ArgumentException)
            {
                throw;
            }
            catch (HttpRequestException)
            {
                throw;
            }
            catch (JsonException ex)
            {
                throw new InvalidOperationException($"Failed to parse profile response: {ex.Message}", ex);
            }
            catch (TaskCanceledException ex)
            {
                throw new TimeoutException($"Get profile request timed out: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Unexpected error while getting profile: {ex.Message}", ex);
            }
        }

    private HttpClient CreateAuthHttpClient(string? accessToken = null)
    {
        // Create a default client instead of using a named client
        var client = _httpClientFactory.CreateClient();
        
        // Set the base address to the Supabase URL
        client.BaseAddress = new Uri(_supabaseUrl);
        
        // Set the API key based on whether we have an access token
        var apiKey = accessToken != null ? _anonKey : _serviceRoleKey;

        // Add headers
        client.DefaultRequestHeaders.Add("apikey", apiKey);
        if (accessToken != null)
        {
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        }
        else
        {
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _serviceRoleKey);
        }

        return client;
    }

        public async Task<SupabaseUser?> GetProfileAsync(string userId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userId))
                    throw new ArgumentException("User ID cannot be null or empty", nameof(userId));

                // Use service role key to get user by ID from auth.users
                using var client = CreateAuthHttpClient();
                var url = $"/auth/v1/admin/users/{userId}";

                var response = await client.GetAsync(url);
                if (!response.IsSuccessStatusCode)
                {
                    if (response.StatusCode == HttpStatusCode.NotFound)
                    {
                        return null; // User not found
                    }
                    response.EnsureSuccessStatusCode(); // This will throw HttpRequestException
                }

                var body = await response.Content.ReadAsStringAsync();
                var user = JsonSerializer.Deserialize<SupabaseUser>(body, _jsonOptions);
                return user;
            }
            catch (ArgumentException)
            {
                throw;
            }
            catch (HttpRequestException)
            {
                throw;
            }
            catch (JsonException ex)
            {
                throw new InvalidOperationException($"Failed to parse user profile response: {ex.Message}", ex);
            }
            catch (TaskCanceledException ex)
            {
                throw new TimeoutException($"Get profile request timed out: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Unexpected error while getting profile: {ex.Message}", ex);
            }
        }

    public async Task<bool> LinkBrokerAsync(string userId, string brokerId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be null or empty", nameof(userId));

            if (string.IsNullOrWhiteSpace(brokerId))
                throw new ArgumentException("Broker ID cannot be null or empty", nameof(brokerId));

            // Use admin API to update user metadata with broker information
            using var client = CreateAuthHttpClient();
            var url = $"/auth/v1/admin/users/{userId}";

            var payload = new
            {
                user_metadata = new
                {
                    broker_id = brokerId
                }
            };

            var json = JsonSerializer.Serialize(payload, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PutAsync(url, content);
            return response.IsSuccessStatusCode;
        }
        catch (ArgumentException)
        {
            throw;
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (TaskCanceledException ex)
        {
            throw new TimeoutException($"Link broker request timed out: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error while linking broker: {ex.Message}", ex);
        }
    }

    // Authentication Methods
    public async Task<AuthResponse> SignupAsync(SignupRequest request)
    {
        try
        {
            using var client = CreateAuthHttpClient();
            var url = $"/auth/v1/signup";

            var payload = new
            {
                email = request.Email,
                password = request.Password,
                phone = request.Phone,
                data = request.Data
            };

            var json = JsonSerializer.Serialize(payload, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                try
                {
                    var error = JsonSerializer.Deserialize<AuthErrorResponse>(responseContent, _jsonOptions);
                    var errorMessage = error?.Msg ?? error?.Message ?? error?.Error ?? error?.ErrorDescription ?? "Unknown error";
                    throw new HttpRequestException($"Signup failed: {errorMessage} (Code: {error?.Code ?? (int)response.StatusCode})");
                }
                catch (JsonException)
                {
                    // If we can't parse the error response, return the raw content
                    throw new HttpRequestException($"Signup failed: {responseContent}");
                }
            }

            // For signup, Supabase returns either a user (if auto-confirm) or session
            var authResponse = JsonSerializer.Deserialize<AuthResponse>(responseContent, _jsonOptions);
            return authResponse ?? throw new InvalidOperationException("Invalid signup response");
        }
        catch (HttpRequestException)
        {
            // Re-throw HTTP request exceptions as they contain meaningful error messages
            throw;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Failed to parse signup response: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            throw new TimeoutException($"Signup request timed out: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error during signup: {ex.Message}", ex);
        }
    }

    public async Task<AuthResponse> LoginAsync(LoginRequest request)
    {
        try
        {
            using var client = CreateAuthHttpClient();
            var url = $"/auth/v1/token?grant_type=password";

            var payload = new
            {
                email = request.Email,
                password = request.Password,
                phone = request.Phone
            };

            var json = JsonSerializer.Serialize(payload, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                try
                {
                    var error = JsonSerializer.Deserialize<AuthErrorResponse>(responseContent, _jsonOptions);
                    var errorMessage = error?.Msg ?? error?.Message ?? error?.Error ?? error?.ErrorDescription ?? "Unknown error";
                    throw new HttpRequestException($"Login failed: {errorMessage} (Code: {error?.Code ?? (int)response.StatusCode})");
                }
                catch (JsonException)
                {
                    // If we can't parse the error response, return the raw content
                    throw new HttpRequestException($"Login failed: {responseContent}");
                }
            }

            var authResponse = JsonSerializer.Deserialize<AuthResponse>(responseContent, _jsonOptions);
            return authResponse ?? throw new InvalidOperationException("Invalid login response");
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Failed to parse login response: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            throw new TimeoutException($"Login request timed out: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error during login: {ex.Message}", ex);
        }
    }

    public async Task<AuthResponse> RefreshTokenAsync(TokenRefreshRequest request)
    {
        try
        {
            using var client = CreateAuthHttpClient();
            var url = $"/auth/v1/token?grant_type=refresh_token";

            var payload = new
            {
                refresh_token = request.RefreshToken
            };

            var json = JsonSerializer.Serialize(payload, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var error = JsonSerializer.Deserialize<AuthErrorResponse>(responseContent, _jsonOptions);
                throw new HttpRequestException($"Token refresh failed: {error?.Message ?? responseContent}");
            }

            var authResponse = JsonSerializer.Deserialize<AuthResponse>(responseContent, _jsonOptions);
            return authResponse ?? throw new InvalidOperationException("Invalid token refresh response");
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Failed to parse token refresh response: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            throw new TimeoutException($"Token refresh request timed out: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error during token refresh: {ex.Message}", ex);
        }
    }

    public async Task<SuccessResponse> RecoverPasswordAsync(PasswordRecoveryRequest request)
    {
        try
        {
            using var client = CreateAuthHttpClient();
            var url = $"/auth/v1/recover";

            var payload = new
            {
                email = request.Email
            };

            var json = JsonSerializer.Serialize(payload, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var error = JsonSerializer.Deserialize<AuthErrorResponse>(responseContent, _jsonOptions);
                throw new HttpRequestException($"Password recovery failed: {error?.Message ?? responseContent}");
            }

            return new SuccessResponse { Message = "Password recovery email sent" };
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Failed to parse password recovery response: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            throw new TimeoutException($"Password recovery request timed out: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error during password recovery: {ex.Message}", ex);
        }
    }

    public async Task<SuccessResponse> SendOtpAsync(OtpRequest request)
    {
        try
        {
            using var client = CreateAuthHttpClient();
            var url = $"/auth/v1/otp";

            var payload = new
            {
                email = request.Email,
                phone = request.Phone,
                create_user = request.CreateUser,
                data = request.Data
            };

            var json = JsonSerializer.Serialize(payload, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var error = JsonSerializer.Deserialize<AuthErrorResponse>(responseContent, _jsonOptions);
                throw new HttpRequestException($"OTP send failed: {error?.Message ?? responseContent}");
            }

            return new SuccessResponse { Message = "OTP sent successfully" };
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Failed to parse OTP response: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            throw new TimeoutException($"OTP request timed out: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error during OTP request: {ex.Message}", ex);
        }
    }

    public async Task<AuthResponse> VerifyOtpAsync(VerifyRequest request)
    {
        try
        {
            using var client = CreateAuthHttpClient();
            var url = $"/auth/v1/verify";

            var payload = new
            {
                email = request.Email,
                phone = request.Phone,
                token = request.Token,
                type = request.Type,
                redirect_to = request.RedirectTo
            };

            var json = JsonSerializer.Serialize(payload, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var error = JsonSerializer.Deserialize<AuthErrorResponse>(responseContent, _jsonOptions);
                throw new HttpRequestException($"OTP verification failed: {error?.Message ?? responseContent}");
            }

            var authResponse = JsonSerializer.Deserialize<AuthResponse>(responseContent, _jsonOptions);
            return authResponse ?? throw new InvalidOperationException("Invalid OTP verification response");
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Failed to parse OTP verification response: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            throw new TimeoutException($"OTP verification request timed out: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error during OTP verification: {ex.Message}", ex);
        }
    }

    public async Task<SupabaseUser> GetUserAsync(string accessToken)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            // Create cache key based on token hash (for security)
            var tokenHash = Convert.ToHexString(System.Security.Cryptography.SHA256.HashData(Encoding.UTF8.GetBytes(accessToken)))[..16];
            var cacheKey = $"user:{tokenHash}";

            // Try to get user from cache first
            var cachedUser = await _cacheService.GetOrSetAsync(cacheKey, async () =>
            {
                using var client = CreateAuthHttpClient(accessToken);
                var url = $"/auth/v1/user";

                var response = await client.GetAsync(url);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    var error = JsonSerializer.Deserialize<AuthErrorResponse>(responseContent, _jsonOptions);
                    throw new HttpRequestException($"Get user failed: {error?.Message ?? responseContent}");
                }

                var user = JsonSerializer.Deserialize<SupabaseUser>(responseContent, _jsonOptions);
                return user ?? throw new InvalidOperationException("Invalid user response");
            }, TimeSpan.FromMinutes(15)); // Cache for 15 minutes

            return cachedUser ?? throw new InvalidOperationException("Failed to get user from cache or API");
        }
        catch (ArgumentException)
        {
            throw;
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Failed to parse user response: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            throw new TimeoutException($"Get user request timed out: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error while getting user: {ex.Message}", ex);
        }
    }

    public async Task<SupabaseUser> UpdateUserAsync(string accessToken, UpdateUserRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            ArgumentNullException.ThrowIfNull(request);

            using var client = CreateAuthHttpClient(accessToken);
            var url = $"/auth/v1/user";

            var payload = new
            {
                email = request.Email,
                password = request.Password,
                phone = request.Phone,
                data = request.Data,
                app_metadata = request.AppMetadata,
                nonce = request.Nonce
            };

            var json = JsonSerializer.Serialize(payload, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PutAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var error = JsonSerializer.Deserialize<AuthErrorResponse>(responseContent, _jsonOptions);
                throw new HttpRequestException($"Update user failed: {error?.Message ?? responseContent}");
            }

            var user = JsonSerializer.Deserialize<SupabaseUser>(responseContent, _jsonOptions);

            // Invalidate cache after user update
            var tokenHash = Convert.ToHexString(System.Security.Cryptography.SHA256.HashData(Encoding.UTF8.GetBytes(accessToken)))[..16];
            var cacheKey = $"user:{tokenHash}";
            await _cacheService.RemoveAsync(cacheKey);

            return user ?? throw new InvalidOperationException("Invalid update user response");
        }
        catch (ArgumentNullException)
        {
            throw;
        }
        catch (ArgumentException)
        {
            throw;
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Failed to parse update user response: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            throw new TimeoutException($"Update user request timed out: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error while updating user: {ex.Message}", ex);
        }
    }

    public async Task<SuccessResponse> LogoutAsync(string accessToken)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            using var client = CreateAuthHttpClient(accessToken);
            var url = $"/auth/v1/logout";

            var response = await client.PostAsync(url, null);

            if (!response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var error = JsonSerializer.Deserialize<AuthErrorResponse>(responseContent, _jsonOptions);
                throw new HttpRequestException($"Logout failed: {error?.Message ?? responseContent}");
            }

            // Clear cache on logout
            var tokenHash = Convert.ToHexString(System.Security.Cryptography.SHA256.HashData(Encoding.UTF8.GetBytes(accessToken)))[..16];
            var cacheKey = $"user:{tokenHash}";
            await _cacheService.RemoveAsync(cacheKey);

            return new SuccessResponse { Message = "Logged out successfully" };
        }
        catch (ArgumentException)
        {
            throw;
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Failed to parse logout response: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            throw new TimeoutException($"Logout request timed out: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error during logout: {ex.Message}", ex);
        }
    }

    // OAuth Methods
    public Task<OAuthResponse> GetOAuthUrlAsync(OAuthRequest request)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.Provider))
                throw new ArgumentException("Provider cannot be null or empty", nameof(request));

            var url = $"{_supabaseUrl}/auth/v1/authorize?provider={request.Provider}";

            if (!string.IsNullOrEmpty(request.RedirectTo))
            {
                url += $"&redirect_to={Uri.EscapeDataString(request.RedirectTo)}";
            }

            if (request.Options != null)
            {
                foreach (var option in request.Options)
                {
                    if (!string.IsNullOrEmpty(option.Key) && !string.IsNullOrEmpty(option.Value))
                    {
                        url += $"&{option.Key}={Uri.EscapeDataString(option.Value)}";
                    }
                }
            }

            var response = new OAuthResponse
            {
                Url = url,
                Provider = request.Provider
            };

            return Task.FromResult(response);
        }
        catch (ArgumentNullException)
        {
            throw;
        }
        catch (ArgumentException)
        {
            throw;
        }
        catch (UriFormatException ex)
        {
            throw new InvalidOperationException($"Invalid URI format in OAuth URL generation: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error during OAuth URL generation: {ex.Message}", ex);
        }
    }

    public async Task<AuthResponse> ExchangeCodeForTokenAsync(string code, string provider)
    {
        try
        {
            using var client = CreateAuthHttpClient();
            var url = $"/auth/v1/token?grant_type=authorization_code";

            var payload = new
            {
                code,
                provider
            };

            var json = JsonSerializer.Serialize(payload, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var error = JsonSerializer.Deserialize<AuthErrorResponse>(responseContent, _jsonOptions);
                throw new HttpRequestException($"OAuth token exchange failed: {error?.Message ?? responseContent}");
            }

            var authResponse = JsonSerializer.Deserialize<AuthResponse>(responseContent, _jsonOptions);
            return authResponse ?? throw new InvalidOperationException("Invalid OAuth token exchange response");
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Failed to parse OAuth token exchange response: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            throw new TimeoutException($"OAuth token exchange request timed out: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error during OAuth token exchange: {ex.Message}", ex);
        }
    }

    // Google Sign-In Methods
    public Task<OAuthResponse> GetGoogleSignInUrlAsync(string? redirectTo = null)
    {
        var request = new OAuthRequest
        {
            Provider = "google",
            RedirectTo = redirectTo
        };

        return GetOAuthUrlAsync(request);
    }

    public Task<AuthResponse> SignInWithGoogleAsync(string code)
    {
        return ExchangeCodeForTokenAsync(code, "google");
    }

    // Phone Sign-In Methods (Twilio)
    public Task<SuccessResponse> SendPhoneOtpAsync(string phoneNumber, bool createUser = true)
    {
        var request = new OtpRequest
        {
            Phone = phoneNumber,
            CreateUser = createUser
        };

        return SendOtpAsync(request);
    }

    public Task<AuthResponse> SignInWithPhoneAsync(string phoneNumber, string otpCode)
    {
        var request = new VerifyRequest
        {
            Phone = phoneNumber,
            Token = otpCode,
            Type = "sms"
        };

        return VerifyOtpAsync(request);
    }

    // Settings
    public async Task<AuthSettingsResponse> GetSettingsAsync()
    {
        try
        {
            using var client = CreateAuthHttpClient();
            var url = $"/auth/v1/settings";

            var response = await client.GetAsync(url);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var error = JsonSerializer.Deserialize<AuthErrorResponse>(responseContent, _jsonOptions);
                throw new HttpRequestException($"Get settings failed: {error?.Message ?? responseContent}");
            }

            var settings = JsonSerializer.Deserialize<AuthSettingsResponse>(responseContent, _jsonOptions);
            return settings ?? throw new InvalidOperationException("Invalid settings response");
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Failed to parse settings response: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            throw new TimeoutException($"Get settings request timed out: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Unexpected error while getting settings: {ex.Message}", ex);
        }
    }
}
